<?php
session_start();
/*
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL & ~E_NOTICE & ~E_WARNING);
*/

require_once '../../defined.conf.php';
require_once '../../model/authenCheck.php';
require_once "../db_config.php";

$RequestURI = $_SERVER['REQUEST_URI'];
$user = $_SESSION['User'];
$title = 'CS-DB';

//!isset($_SESSION['userdata'])

?>

<!doctype html>
<html lang="en">
<title>CS-DB Customer Profiles</title>
<meta http-equiv="content-type" content="text/html; charset=tis-620" />
<link rel="icon" href="../img/Favicon.png" sizes="32x32">
<link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.0.8/css/all.css">
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.11.5/css/jquery.dataTables.css">
<link rel="stylesheet" href="../operation/opt.css" type="text/css">
</head>

<body>
    <?php

    if (!$user && !isset($_SESSION['userdata'])) {
        echo "<SCRIPT language=JavaScript>self.location='../login/index.php?request=$RequestURI';</SCRIPT>";
    }

    include_once "../operation/menu_csdb.php";

    $conn = getDBConnection();
    mysqli_select_db($conn, "KCS_DB");

    $cuscode = @$_POST["cuscode"];
    $scus_id = @$_GET["scus_id"];
    $ref_id = $_REQUEST["ref_id"]??'';
    $login = @$_POST["login"];
    $no1 = @$_POST["no1"];
    $no2 = @$_POST["no2"];
    $dum1 = @$_POST["dum1"];
    $dum2 = @$_POST["dum2"];

    $cus_query = "";
    $sql = "SELECT m.*,c.CusName FROM  Main m LEFT JOIN Customers  c ON m.CusCode = c.CusCode";
    $sql_where = "";
    if (@$_POST['cusid_find']) {
        $sql_where = " CusCode = '" . mysqli_real_escape_string($conn, $cuscode) . "'";
    } else if (@$_REQUEST['ref_find']) {
        $sql_where = " Ref = '" . mysqli_real_escape_string($conn, $ref_id) . "'";
    } else if (@$_POST['login_find']) {
        $sql_where = " Login = '" . mysqli_real_escape_string($conn, $login) . "'";
    } else if (@$_POST['no1_find']) {
        $sql_where = " Connect_No = '" . mysqli_real_escape_string($conn, $no1) . "'";
    } else if (@$_POST['no2_find']) {
        $sql_where = " Connect_No2 = '" . mysqli_real_escape_string($conn, $no2) . "'";
    } else if (@$_POST['dum1_find']) {
        $sql_where = " Dum1 = '" . mysqli_real_escape_string($conn, $dum1) . "'";
    } else if (@$_POST['dum2_find']) {
        $sql_where = " Dum2 = '" . mysqli_real_escape_string($conn, $dum2) . "'";
    } else if ($scus_id) {
        $sql_where = " Cus_ID = '" . mysqli_real_escape_string($conn, $scus_id) . "'";
    }
    if ($sql_where) {
        $sql = $sql . " WHERE " . $sql_where;
        $cus_query = mysqli_query($conn, $sql);
    }

    if ($cus_query != "") {
        //$cus = mysql_fetch_assoc($cus_query);
        $cus = mysqli_fetch_array($cus_query, MYSQLI_BOTH);
        //var_dump($cus);

        if ($cus['Cus_ID']) {
            $sql_main_detail = "SELECT * FROM Main_Detail WHERE scus_id='" . mysqli_real_escape_string($conn, $cus['Cus_ID']) . "';";
            $main_detail_rst = mysqli_query($conn, $sql_main_detail);
            $main_detail = mysqli_fetch_array($main_detail_rst, MYSQLI_BOTH);

            //echo "<p> sql_main_detail= $sql_main_detail </p>";
            // var_dump($main_detail);

            if ($main_detail) {
                $cus =  array_merge($cus, $main_detail);
            }

            $sql_mon = "SELECT  mt.Project_Mon_Type_Name,md.Project_Mon_Date_Name,mtm.Project_Mon_Time_Name
                                FROM Main m
                                LEFT JOIN Project_Mon_Type mt ON m.Project_Mon_Type_ID = mt.Project_Mon_Type_ID
                                LEFT JOIN Project_Mon_Day md ON m.Project_Mon_Date_ID = md.Project_Mon_Date_ID
                                LEFT JOIN Project_Mon_Time mtm ON m.Project_Mon_Time_ID = mtm.Project_Mon_Time_ID
                                WHERE
                                m.Cus_ID =" . mysqli_real_escape_string($conn, $cus['Cus_ID'])  . "; ";

            $monitor_rst = mysqli_query($conn, $sql_mon);
            $monitor = mysqli_fetch_array($monitor_rst, MYSQLI_BOTH);
            if ($monitor) {
                $cus =  array_merge($cus, $monitor);
            }
        } //    if($cus['Cus_ID'])

    }

    ?>

    <table width="100%" border="0" cellpadding="0" cellspacing="0">
        <tr bgcolor="#000099">
            <td width="300" height="24" align="center" bgcolor="#ffddbb" class="bbody" style="border-right:solid #000 1px"></td>
            <td width="250" align="center" bgcolor="#EEEEEE" class="bbody" onClick="window.open('data_compare_monitor_wizard.php','_blank')" onMouseOver="this.className = 'over3'" onMouseOut="this.className = 'bbody'">[<b>Data compare monitor wizard</b>]</td>
            <td width="150" align="center" bgcolor="#EEEEEE" class="bbody" onClick="window.open('customers_list.php','_blank')" onMouseOver="this.className = 'over3'" onMouseOut="this.className = 'bbody'">[<b>Customers</b>]</td>
            <td width="150" align="center" bgcolor="#EEEEEE" class="bbody" onClick="window.open('project_list.php','_blank')" onMouseOver="this.className = 'over3'" onMouseOut="this.className = 'bbody'">[<b>Projects</b>]</td>
            <td></td>
            <td></td>
            <td bgcolor="#EEEEEE">&nbsp;</td>
        </tr>
    </table>
    <table width="100%" border="0" cellspacing="0" cellpadding="0" bgcolor="#ffddbb">
        <form name="form1" method="post" action="">
            <tr>
                <td style="padding-left:15px; padding-right:15px" align="center">
                    <br />
                    <input type="button" name="button" id="button" value="Advance Search" onClick="link('search.php')" style="margin-right:  30px;">

                    <?php if (@$cus['Project_ID']) echo "<input type=\"button\" name=\"button\" id=\"button\" value=\"Edit\" onClick=\"link('edit.php?cus_id=" . @$cus['Cus_ID'] . "')\">"; ?><br /><br />
                    <hr color="#FF3333" /><br />
                </td>
            </tr>
            <tr>
                <td class="bbbody" style="padding-left:15px; padding-right:15px">ADDRESS / NUMBER Connection<br /><br />
                    <table border="0" cellspacing="0" cellpadding="0">
                        <tr>
                            <?php
                            //$project_query = mysqli_query($conn, "Select Project_Name, Project_DetailName, Project_Password,Cacti_URL,Cacti_Acc,VRF,LoopBack  From Project where Project_ID = '" . $cus['Project_ID'] . "'");
                            $project_id=0;
                            if(@$cus['Project_ID']){
                                $project_id=$cus['Project_ID'];
                            }
                            $proj_query = ' SELECT 
p.Project_Name,p.`Owner`, p.Project_DetailName, p.Project_Password,p.Cacti_URL
,p.Cacti_Acc,p.VRF,LoopBack,pt.Project_Mon_Type_Name,mond.Project_Mon_Date_Name
,mont.Project_Mon_Time_Name
,CONCAT(mont.Project_Mon_Time_Name,"[",mont.Project_Mon_Time_Start,"-",mont.Project_Mon_Time_End
,"]")AS Mon_Time  FROM Project p
LEFT JOIN Project_Mon_Type pt ON p.Project_Mon_Type_ID=pt.Project_Mon_Type_ID
LEFT JOIN Project_Mon_Day mond ON p.Project_Mon_Date_ID=mond.Project_Mon_Date_ID
LEFT JOIN Project_Mon_Time mont ON p.Project_Mon_Time_ID = mont.Project_Mon_Time_ID
WHERE p.Project_ID = ' . $project_id . ' ; ';

                            $project_query = mysqli_query($conn, $proj_query);
                            //$project = mysql_fetch_row($project_query);
                            $project = mysqli_fetch_array($project_query, MYSQLI_BOTH);
                            ?>
                            <td height="24" bgcolor="#ffcc99" class="bbbody" style="padding-left:5px; border:solid #999 1px"><a title="<?= @$project['Project_DetailName'] ?>"><?= @$project['Project_Name'] ?>&nbsp;</a></td>
                            <td width="140" height="24" bgcolor="#ffcc99" class="bbody" style="padding-left:5px; border-bottom:solid #999 1px; border-top:solid #999 1px; border-right:solid #999 1px"><a title="Project Password"><?= @$project['Project_Password'] ?>&nbsp;</a></td>
                            <td width="50" class="bbody" align="right" style="padding-right:5px">IROP</td>
                            <td width="100" bgcolor="#ffcc99" style="padding-left:5px; border:solid #999 1px" class="bbody"><?= @$cus['IROP'] ?>&nbsp;</td>
                            <td class="bbody" style="padding-left:5px">
                                <!--input id="cusid_find" name="cusid_find" type="submit" value="Cus. Code"  onKeyPress="searchKeyPress(event, 'cusid_find');" -->
                                <label for="cuscode"> Cus.Code(CTD)</label>
                                <input name="cuscode" type="text" id="cuscode" style="background-color:#ffcc99" value="<?= @$cus['CusCode'] ?>">

                            </td>

                            <td width="50" class="bbody" align="right" style="padding-right:5px">VRF</td>
                            <td width="100" bgcolor="#ffcc99" style="padding-left:5px; border:solid #999 1px" class="bbody"><?= @$project['VRF'] ?>&nbsp;</td>
                            <td width="50" class="bbody" align="right" style="padding-left:10px; padding-right:5px">LoopBack</td>
                            <td width="100" bgcolor="#ffcc99" style="padding-left:5px; border:solid #999 1px" class="bbody"><?= @$project['LoopBack'] ?>&nbsp;</td>
                        </tr>
                    </table><br /><br />
                    <table border="0" cellpadding="0" cellspacing="0">
                        <tr>
                            <td align="right">
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <td width="140" height="24" align="right" class="bbody" style="padding-right:5px">Customer Name</td>
                                        <td colspan="3" bgcolor="#ffcc99" style="padding-left:5px; border:solid #999 1px" class="bbody">
                                            <label id="cusname"><?= @$cus['CusName'] ?></label>
                                        </td>
                                    </tr>
                                    <tr>
                                    <tr>
                                        <td height="24" align="right" class="bbody" style="padding-right:5px">
                                            CS ID
                                        </td>
                                        <td width="100" bgcolor="#99ccff" style="padding-left:5px; border:solid #999 1px" class="bbody">
                                            <?= @$cus['Cus_ID'] ?>
                                        </td>
                                        <td width="50" class="bbody" align="right" style="padding-right:5px">Owner</td>
                                        <td bgcolor="#99ccff" style="padding-left:5px; border:solid #999 1px" class="bbody"><?= @$project['Owner'] ?>&nbsp;</td>
                                    </tr>
                                    <tr>
                                        <td height="24" align="right" class="bbody" style="padding-right:5px">Type</td>
                                        <?php
                                        $sitetype_query = mysqli_query($conn, "select Site_Name from Site_Type where Site_ID = '" .@ $cus['Site_Type'] . "'");
                                        // $sitetype = mysql_fetch_row($sitetype_query);
                                        $sitetype = mysqli_fetch_array($sitetype_query, MYSQLI_BOTH);

                                        $place_query = mysqli_query($conn, "select Shop_Name from Place where Shop_ID = '" . @$cus['Place_ID'] . "'");
                                        //$place = mysql_fetch_row($place_query);
                                        $place = mysqli_fetch_array($place_query, MYSQLI_BOTH);
                                        ?>
                                        <td width="100" bgcolor="#99ccff" style="padding-left:5px; border:solid #999 1px" class="bbody"><?= @$sitetype['Site_Name'] ?>&nbsp;</td>
                                        <td width="50" class="bbody" align="right" style="padding-right:5px">Place</td>
                                        <td width="286" bgcolor="#99ccff" style="padding-left:5px; border:solid #999 1px" class="bbody"><?= @$place['Shop_Name'] ?>&nbsp;</td>
                                    </tr>
                                </table>
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <td height="24" align="right" class="bbody" style="padding-right:5px">Site Name</td>
                                        <td width="450" bgcolor="#99ccff" style="padding-left:5px; border:solid #999 1px" class="bbody"><?= @$cus['Site_Name'] ?>&nbsp;</td>
                                    </tr>
                                </table>
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <td height="24" align="right" class="bbody" style="padding-right:5px">Site Address</td>
                                        <td width="445" rowspan="3" bgcolor="#99ccff" class="bbody" style="padding:5 5 5 5; border:solid #999 1px" valign="top"><?= @$cus['Site_Address'] ?>&nbsp;</td>
                                    </tr>
                                    <tr>
                                        <td height="24" align="right" class="bbody" style="padding-right:5px">&nbsp;</td>
                                    </tr>
                                    <tr>
                                        <td height="24" align="right" class="bbody" style="padding-right:5px">&nbsp;</td>
                                    </tr>
                                </table>
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <td height="24" align="right" class="bbody" style="padding-right:5px">1 ST_Customer Contact</td>
                                        <td width="445" rowspan="2" bgcolor="#99ccff" class="bbody" style="padding:5 5 5 5; border:solid #999 1px" valign="top"><?= @$cus['Phone1'] ?>&nbsp;</td>
                                    </tr>
                                    <tr>
                                        <td height="24" align="right" class="bbody" style="padding-right:5px">&nbsp;</td>
                                    </tr>
                                </table>
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <td height="24" align="right" class="bbody" style="padding-right:5px">2 ND_Customer Contact</td>
                                        <td width="445" rowspan="2" bgcolor="#99ccff" class="bbody" style="padding:5 5 5 5; border:solid #999 1px" valign="top"><?= @$cus['Phone2'] ?>&nbsp;</td>
                                    </tr>
                                    <tr>
                                        <td height="24" align="right" class="bbody" style="padding-right:5px">&nbsp;</td>
                                    </tr>
                                </table>

                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <td height="24" align="right" class="bbody" style="padding-right:5px">Contact Sale</td>
                                        <td width="445" bgcolor="#ccffff" class="bbody" style="padding:5 5 5 5; border:solid #999 1px;" valign="top">&nbsp;<?= @$cus['Contact_Sale'] ?></td>
                                    </tr>
                                    <tr>
                                        <td height="24" align="right" class="bbody" style="padding-right:5px">Contact PM</td>
                                        <td width="445" bgcolor="#ccffff" class="bbody" style="padding:5 5 5 5; border:solid #999 1px" valign="top">&nbsp;<?= @$cus['Contact_PM'] ?></td>
                                    </tr>
                                </table>
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <td align="right" rowspan="2" class="bbody" style="padding-right:5px" valign="top">Terms of Service</td>
                                        <td align="right" class="bbody" style="padding-right:5px" width="60">SLA</td>
                                        <td bgcolor="#ccffff" class="bbody" style="padding:5 5 5 5; border:solid #999 1px" width="150">&nbsp;<?= @$cus['SLA'] ?></td>
                                        <td align="right" class="bbody" style="padding-right:5px">SMS Alert</td>
                                        <td bgcolor="#ccffff" class="bbody" style="padding:5 5 5 5; border:solid #999 1px" width="150">&nbsp;<?= @$cus['SMS_Alert'] ?></td>
                                        <td> </td>
                                    </tr>
                                    <tr>

                                        <td align="right" class="bbody" style="padding-right:5px">MTTR</td>
                                        <td bgcolor="#ccffff" class="bbody" style="padding:5 5 5 5; border:solid #999 1px" valign="top">&nbsp;<?= @$cus['MTTR'] ?></td>
                                        <td align="right" class="bbody" style="padding-right:5px">Mail Alert</td>
                                        <td bgcolor="#ccffff" class="bbody" style="padding:5 5 5 5; border:solid #999 1px" valign="top">&nbsp;<?= @$cus['Mail_Alert'] ?></td>
                                        <td> </td>
                                    </tr>

                                </table>
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <td align="right" class="bbody" style="padding-right:5px" valign="top">Surveillance Type</td>
                                        <td bgcolor="#ccffff" class="bbody" style="padding:5 5 5 5; border:solid #999 1px" width="94">&nbsp;<?= @$cus['Project_Mon_Type_Name'] ?></td>

                                        <td align="right" class="bbody" style="padding-right:5px">Monitor Days</td>
                                        <td bgcolor="#ccffff" class="bbody" style="padding:5 5 5 5; border:solid #999 1px" width="70">&nbsp;<?= @$cus['Project_Mon_Date_Name'] ?></td>

                                        <td align="right" class="bbody" style="padding-right:5px">Monitor Time</td>
                                        <td bgcolor="#ccffff" class="bbody" style="padding:5 5 5 5; border:solid #999 1px" width="94">&nbsp;<?= @$cus['Project_Mon_Time_Name'] ?></td>
                                    </tr>
                                </table>

                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <td height="24" align="right" class="bbody" style="padding-right:5px">FAX1</td>
                                        <td width="450" bgcolor="#99ccff" class="bbody" style="padding-left:5px; border:solid #999 1px"><?= @$cus['FAX1'] ?>&nbsp;</td>
                                    </tr>
                                </table>
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <?php
                                        $province_query = mysqli_query($conn, "select province_Name from Province where province_ID = '" . @$cus['Province_ID'] . "'");
                                        //$province = mysql_fetch_row($province_query);
                                        $province = mysqli_fetch_array($province_query, MYSQLI_BOTH);
                                        ?>
                                        <td height="24" align="right" class="bbody" style="padding-right:5px">Province</td>
                                        <td width="450" bgcolor="#99ccff" style="padding-left:5px; border:solid #999 1px" class="bbody"><?= @$province['province_Name'] ?>&nbsp;</td>
                                    </tr>
                                </table>
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <td height="24" align="right" class="bbody" style="padding-right:5px">Map</td>
                                        <td width="198" bgcolor="#99ccff" style="padding-left:5px; border:solid #999 1px" class="bbody"><?= @$cus['Map'] ?>&nbsp;</td>
                                        <td width="50" align="right" class="bbody" style="padding-right:5px">ZIP</td>
                                        <td width="188" bgcolor="#99ccff" style="padding-left:5px; border:solid #999 1px" class="bbody"><?= @$cus['ZIP'] ?>&nbsp;</td>
                                    </tr>
                                </table>
                            </td>
                            <td align="right" valign="top">
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <td><input id="no1_find" name="no1_find" type="submit" value="No.1">
                                            <input name="no1" type="text" id="no1" style="background-color:#99ccff" onKeyPress="searchKeyPress(event, 'no1_find');" value="<?= @$cus['Connect_No'] ?>" size="10" maxlength="15">
                                        </td>
                                        <td style="padding-left:5px"><input id="dum1_find" name="dum1_find" type="submit" value="Dummy1">
                                            <input name="dum1" type="text" id="dum1" style="background-color:#99ccff" onKeyPress="searchKeyPress(event, 'dum1_find');" value="<?= @$cus['Dum1'] ?>" size="10" maxlength="15">
                                        </td>
                                        <?php
                                        $operator_query = mysqli_query($conn, "select * from Oprator where Oprator_ID = '" . @$cus['Operator_ID'] . "'");
                                        //$operator = mysql_fetch_row($operator_query);
                                        $operator1 = mysqli_fetch_array($operator_query, MYSQLI_BOTH);

                                        $m_opt = @$operator1['Oprator_Name'];
                                        ?>
                                        <td width="200" bgcolor="#99ccff" style="padding-left:5px; border:solid #999 1px" class="bbody">
                                            <a title="<?= @$operator1['Oprator_Type'] ?>"><?= @$operator1['Oprator_Name'] ?></a>&nbsp;
                                        </td>
                                    </tr>
                                </table>
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <td>
                                            <input id="no2_find" name="no2_find" type="submit" value="No.2">
                                            <input name="no2" type="text" id="no2" style="background-color:#99ccff" onKeyPress="searchKeyPress(event, 'no2_find');" value="<?= @$cus['Connect_No2'] ?>" size="10" maxlength="15">
                                        </td>
                                        <td style="padding-left:5px">
                                            <input id="dum2_find" name="dum2_find" type="submit" value="Dummy2">
                                            <input name="dum2" type="text" id="dum2" style="background-color:#99ccff" onKeyPress="searchKeyPress(event, 'dum2_find');" value="<?= @$cus['Dum2'] ?>" size="10" maxlength="15">
                                        </td>
                                        <?php
                                        $operator_query = mysqli_query($conn, "select * from Oprator where Oprator_ID = '" . @$cus['Operator_ID2'] . "'");
                                        //$operator = mysql_fetch_row($operator_query);
                                        $operator = mysqli_fetch_array($operator_query, MYSQLI_BOTH);
                                        ?>
                                        <td width="200" bgcolor="#99ccff" style="padding-left:5px; border:solid #999 1px" class="bbody"><a title="<?=@$operator['Oprator_Type'] ?>"><?= @$operator['Oprator_Name'] ?></a>&nbsp;</td>
                                    </tr>
                                </table>
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <?php
                                        $speed_query = mysqli_query($conn, "select * from Speed where Speed_ID = '" . @$cus['Speed_ID'] . "'");
                                        // $speed = mysql_fetch_row($speed_query);
                                        $speed = mysqli_fetch_array($speed_query, MYSQLI_BOTH);
                                        ?>
                                        <td height="24" align="right" class="bbody" style="padding-right:5px">Speed</td>
                                        <td width="145" bgcolor="#99ccff" class="bbody" style="padding-left:5px; border:solid #999 1px"><?= @$speed['Speed_Name'] ?>&nbsp;</td>

                                        <td height="24" align="right" class="bbody" style="padding-right:5px">Network Layer</td>
                                        <td width="150" bgcolor="#ccffff" class="bbody" style="padding-left:5px; border:solid #999 1px">&nbsp;<?= @$cus['Network_Layer'] ?></td>

                                    </tr>
                                </table>
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <td height="24" align="right" class="bbody" style="padding-right:5px">Vlan No.</td>
                                        <td width="90" bgcolor="#ccffff" class="bbody" style="padding-left:5px; border:solid #999 1px">&nbsp;<?= @$cus['Client_Vlan_No'] ?></td>
                                        <td></td>
                                        <td></td>
                                    </tr>
                                    <tr>
                                        <td height="24" align="right" class="bbody" style="padding-right:5px">IP WAN</td>
                                        <td width="90" bgcolor="#ccffff" class="bbody" style="padding-left:5px; border:solid #999 1px">&nbsp;<?= @$cus['Client_WAN'] ?></td>
                                        <td></td>
                                        <td></td>
                                    </tr>

                                    <tr>
                                        <td height="24" align="right" class="bbody" style="padding-right:5px">IP GW</td>
                                        <td width="90" bgcolor="#ccffff" class="bbody" style="padding-left:5px; border:solid #999 1px">&nbsp;<?= @$cus['Client_GW'] ?></td>
                                        <td></td>
                                        <td></td>
                                    </tr>

                                    <tr>
                                        <td height="24" align="right" class="bbody" style="padding-right:5px">Radius User/Pass</td>
                                        <td width="180" bgcolor="#ccffff" class="bbody" style="padding-left:5px; border:solid #999 1px">&nbsp;<?= @$cus['Client_Radius_User'] ?></td>

                                        <td height="24" align="right" class="bbody" style="padding-right:5px" width="16">/</td>
                                        <td width="180" bgcolor="#ccffff" class="bbody" style="padding-left:5px; border:solid #999 1px">&nbsp;<?= @$cus['Client_Radius_Pass'] ?></td>
                                    </tr>
                                </table>

                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <?php
                                        $dslin_query = mysqli_query($conn, "select * from DSL_Installation where Status_ID = '" . @$cus['DSL_Installation'] . "'");
                                        //$dslin = mysql_fetch_row($dslin_query);
                                        $dslin = mysqli_fetch_array($dslin_query, MYSQLI_BOTH);
                                        ?>
                                        <td height="24" align="right" class="bbody" style="padding-right:5px">DSL Installation</td>
                                        <td width="390" bgcolor="#99ccff" class="bbody" style="padding-left:5px; border:solid #999 1px"><a title="<?= $dslin['Detail'] ?>"><?= @$dslin['Status_Name'] ?></a>&nbsp;</td>
                                    </tr>
                                </table>
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <td height="24" align="right" class="bbody" style="padding-right:5px">DSL Installation Date</td>
                                        <td width="390" bgcolor="#99ccff" class="bbody" style="padding-left:5px; border:solid #999 1px"><?= @$cus['DSL_Installation_Date'] ?>&nbsp;</td>
                                    </tr>
                                </table>
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <td height="24" align="right" class="bbody" style="padding-right:5px">SNR</td>
                                        <td width="80" bgcolor="#99ccff" style="padding-left:5px; border:solid #999 1px" class="bbody"><?= @$cus['DownSNR'] ?>&nbsp;</td>
                                        <td class="bbody" style="padding-right:5px; padding-left:5px">/</td>
                                        <td width="80" bgcolor="#99ccff" style="padding-left:5px; border:solid #999 1px" class="bbody"><?= @$cus['UpSNR'] ?>&nbsp;</td>
                                        <td width="205">&nbsp;</td>
                                    </tr>
                                </table>
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <td height="24" align="right" class="bbody" style="padding-right:5px">Att</td>
                                        <td width="80" bgcolor="#99ccff" style="padding-left:5px; border:solid #999 1px" class="bbody"><?= @$cus['DownAtt'] ?>&nbsp;</td>
                                        <td class="bbody" style="padding-right:5px; padding-left:5px">/</td>
                                        <td width="80" bgcolor="#99ccff" style="padding-left:5px; border:solid #999 1px" class="bbody"><?= @$cus['UpAtt'] ?>&nbsp;</td>
                                        <td width="205">&nbsp;</td>
                                    </tr>
                                </table>
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <td height="24" align="right" class="bbody" style="padding-right:5px">Service Order</td>
                                        <td width="80" bgcolor="#99ccff" style="padding-left:5px; border:solid #999 1px" class="bbody"><?= @$cus['Service_Order'] ?>&nbsp;</td>
                                        <td class="bbody" style="padding-right:5px; padding-left:5px">Req</td>
                                        <td width="80" bgcolor="#99ccff" style="padding-left:5px; border:solid #999 1px" class="bbody"><?= @$cus['Request'] ?>&nbsp;</td>
                                        <td width="188">&nbsp;</td>
                                    </tr>
                                </table>
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <td height="24" align="right" class="bbody" style="padding-right:5px">DDNS Name</td>
                                        <td width="201" bgcolor="#FFFFFF" class="bbody" style="padding-left:5px; border:solid #999 1px"><?= @$cus['DDNS_Name'] ?>&nbsp;</td>
                                        <td width="188">&nbsp;</td>
                                    </tr>

                                </table>
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <?php
                                        $domain_query = mysqli_query($conn, "select * from Domain where Domain_ID = '" . @$cus['DDNS_Domain_ID'] . "'");
                                        //$domain = mysql_fetch_row($domain_query);
                                        $domain = mysqli_fetch_array($domain_query, MYSQLI_BOTH);
                                        ?>
                                        <td height="24" align="right" class="bbody" style="padding-right:5px">DDNS Domain</td>
                                        <td width="201" bgcolor="#FFFFFF" class="bbody" style="padding-left:5px; border:solid #999 1px"><?= @$domain['Doamin_Name'] ?>&nbsp;</td>
                                        <td width="188">&nbsp;</td>
                                    </tr>
                                    <tr>
                                        <td height="24" align="right" class="bbody" style="padding-right:5px">Operator contact</td>
                                        <td colspan="2" bgcolor="#FFFFFF" class="bbody" style="padding-left:5px; border:solid #999 1px"><?= @$operator1['Oprator_contact'] ?>&nbsp;</td>

                                    </tr>
                                </table>

                            </td>
                        </tr>
                    </table><br />
                    <hr color="#FF3333" /><br />
                </td>
            </tr>
            <tr>
                <td class="bbbody" style="padding-left:15px; padding-right:15px"><b>Connection Info</b><br /><br />
                    <table border="0" cellspacing="0" cellpadding="5">
                        <tr>
                            <td align="right">
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <?php
                                        $conntype_query = mysqli_query($conn, "select * from Connection_Type where Connection_ID = '" . @$cus['Connection_Type'] . "'");
                                        // $conntype = mysql_fetch_row($conntype_query);
                                        $conntype = mysqli_fetch_array($conntype_query, MYSQLI_BOTH);
                                        ?>
                                        <td height="24" align="right" class="bbody" style="padding-right:5px">Product Type</td>
                                        <td width="350" bgcolor="#00ffff" class="bbbody" style="padding-left:5px; border:solid #999 1px"><a title="<?= @$conntype['Con_Type'] ?>"><?= @$conntype['Connection_Type'] ?></a>&nbsp;</td>

                                    </tr>
                                </table>
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <td class="bbody">CS No.<input id="ref_find" name="ref_find" type="submit" value="Ref"><input name="ref_id" type="text" id="ref_id" style="background-color:#ffcc99" value="<?= @$cus['Ref'] ?>" size="48" onKeyPress="searchKeyPress(event, 'ref_find');"></td>
                                    </tr>
                                </table>

                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <?php
                                        $domain_query = mysqli_query($conn, "select * from Domain where Domain_ID = '" . @$cus['Domain_ID'] . "'");
                                        // $domain = mysql_fetch_row($domain_query);
                                        $domain = mysqli_fetch_array($domain_query, MYSQLI_BOTH);

                                        $m_domain = @$domain['Doamin_Name'];
                                        ?>
                                        <td>
                                            <input id="login_find" name="login_find" type="submit" value="Con Login">
                                            <input name="login" type="text" id="login" style="background-color:#ccffff;width:160;" value="<?= @$cus['Login'] ?>" onKeyPress="searchKeyPress(event, 'login_find');">
                                        </td>
                                        <td width="148" bgcolor="#ccffff" style="padding-left:5px; border:solid #999 1px" class="bbody"><?= @$domain['Doamin_Name'] ?>&nbsp;</td>
                                    </tr>
                                </table>
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <td height="24" align="right" class="bbody" style="padding-right:5px">Password</td>
                                        <td width="308" bgcolor="#ccffff" style="padding-left:5px; border:solid #999 1px" class="bbody"><?= @$cus['Password'] ?>&nbsp;</td>
                                    </tr>
                                </table>
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <td height="24" align="right" class="bbody" style="padding-right:5px">WAN IP</td>
                                        <td width="152" bgcolor="#ccffff" style="padding-left:5px; border:solid #999 1px; color:#F00" class="bbbody"><?= @$cus['WAN_IP'] ?>&nbsp;</td>
                                        <td style="text-align: center">&nbsp;/&nbsp;</td>
                                        <td width="148" bgcolor="#ccffff" style="padding-left:5px; border:solid #999 1px" class="bbody"><?=@$cus['WAN_Sub'] ?>&nbsp;</td>

                                    </tr>

                                    <tr>
                                        <td height="24" align="right" class="bbody" style="padding-right:5px">WAN GW</td>
                                        <td width="152" bgcolor="#ccffff" style="padding-left:5px; border:solid #999 1px; color:#F00" class="bbbody"><?= @$cus['WAN_GW'] ?>&nbsp;</td>
                                        <td></td>
                                        <td></td>
                                    </tr>
                                    <tr>
                                        <td height="24" align="right" class="bbody" style="padding-right:5px">MPLS Node</td>
                                        <td width="152" bgcolor="#99ccff" style="padding-left:5px; border:solid #999 1px; color:#F00" class="bbbody">&nbsp;<?= @$cus['MPLS_Node'] ?></td>
                                        <td height="24" align="right" class="bbody" style="padding-right:5px;padding-left: 5px;">Vlan No.</td>
                                        <td width="152" bgcolor="#99ccff" style="padding-left:5px; border:solid #999 1px; color:#F00" class="bbbody">&nbsp;<?= @$cus['Jinet_Vlan_No'] ?></td>
                                    </tr>
                                    <tr>
                                        <td height="24" align="right" class="bbody" style="padding-right:5px">ASN</td>
                                        <td width="152" bgcolor="#ccffff" style="padding-left:5px; border:solid #999 1px; color:#F00" class="bbbody"><?= @$cus['ASN'] ?>&nbsp;</td>
                                        <td></td>
                                        <td></td>
                                    </tr>
                                </table>
                            </td>
                            <td width="350" align="right" valign="bottom">
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <td class="bbody" style="padding-right:5px; text-align: right;">CORP_Interface</td>
                                        <td width="310" bgcolor="#ccffcc" style="padding-left:5px; border:solid #999 1px" class="bbody"><?= @$cus['CORP_Interface'] ?></td>
                                    </tr>
                                    <tr>
                                        <td class="bbody" style="padding-right:5px; text-align: right;">VSI_Name</td>
                                        <td width="310" bgcolor="#ccffcc" style="padding-left:5px; border:solid #999 1px" class="bbody"><?= @$cus['VSI_Name'] ?></td>
                                    </tr>
                                    <tr>
                                        <td class="bbody" style="padding-right:5px; text-align: right;">VPN-Instance</td>
                                        <td width="310" bgcolor="#ccffcc" style="padding-left:5px; border:solid #999 1px" class="bbody"><?= @$cus['vpn_instance'] ?></td>
                                    </tr>
                                    <tr>
                                        <td class="bbody" style="padding-right:5px; text-align: right;">Node_ME</td>
                                        <td width="310" bgcolor="#ccffcc" style="padding-left:5px; border:solid #999 1px" class="bbody"><?= @$cus['Node_ME'] ?></td>
                                    </tr>
                                    <tr>
                                        <td class="bbody" style="padding-right:5px; text-align: right;">ME_Interface</td>
                                        <td width="310" bgcolor="#ccffcc" style="padding-left:5px; border:solid #999 1px" class="bbody"><?= @$cus['ME_Interface'] ?></td>
                                    </tr>
                                </table>
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <?php
                                        $domain_query = mysqli_query($conn, "select * from Domain where Domain_ID = '" . @$cus['Link_Domain_ID'] . "'");
                                        //$domain = mysql_fetch_row($domain_query);
                                        $domain = mysqli_fetch_array($domain_query, MYSQLI_BOTH);
                                        ?>
                                        <td height="24" align="right" class="bbody" style="padding-right:5px">Link</td>
                                        <td width="155" bgcolor="#ccffcc" style="padding-left:5px; border:solid #999 1px" class="bbody"><?= @$cus['Link_Login'] ?>&nbsp;</td>
                                        <td width="147" bgcolor="#ccffcc" style="padding-left:5px; border:solid #999 1px" class="bbody"><?= @$domain['Doamin_Name'] ?>&nbsp;</td>
                                    </tr>
                                </table>
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <td height="24" align="right" class="bbody" style="padding-right:5px">PW</td>
                                        <td width="310" bgcolor="#ccffcc" style="padding-left:5px; border:solid #999 1px" class="bbody"><?= @$cus['Link_Password'] ?>&nbsp;</td>
                                    </tr>
                                </table>
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <td height="24" align="right" class="bbody" style="padding-right:5px">LIP</td>
                                        <td width="155" bgcolor="#ccffcc" style="padding-left:5px; border:solid #999 1px; color:#00F" class="bbbody"><?= @$cus['Link_IP'] ?>&nbsp;</td>
                                        <td width="147" bgcolor="#ccffcc" style="padding-left:5px; border:solid #999 1px" class="bbody"><?= @$cus['Link_Sub'] ?>&nbsp;</td>
                                    </tr>
                                </table>
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <td height="24" align="right" class="bbody" style="padding-right:5px">T2</td>
                                        <td width="155" bgcolor="#ccffcc" style="padding-left:5px; border:solid #999 1px" class="bbody"><?= @$cus['Tunnel2'] ?>&nbsp;</td>
                                        <td width="147" bgcolor="#ccffcc" style="padding-left:5px; border:solid #999 1px" class="bbody"><?= @$cus['Tunnel2sub'] ?>&nbsp;</td>
                                    </tr>
                                </table>
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <?php
                                        $restart_query = mysqli_query($conn, "select * from Restart where Restart_Router_ID = '" . @$cus['Restart_Router'] . "'");
                                        //$restart = mysql_fetch_row($restart_query);
                                        $restart = mysqli_fetch_array($restart_query, MYSQLI_BOTH);
                                        ?>

                                        <td width="147" height="24" bgcolor="#ffff99" class="bbody" style="padding-left:5px; border:solid #999 1px"><?= @$restart['Restart_Name'] ?>&nbsp;</td>
                                    </tr>
                                </table>
                            </td>
                            <td width="350" align="right" valign="top">
                             <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <?php
                                        $status_query = mysqli_query($conn, "select * from Status where Status_ID = '" . @$cus['Status_ID'] . "'");
                                        //$status = mysql_fetch_row($status_query);
                                        $status = mysqli_fetch_array($status_query, MYSQLI_BOTH);
                                        $bg_color = "#ffcc99";
                                        if ($status['Status_Name'] == "Terminate") {
                                            $bg_color = "red";
                                        }
                                        ?>
                                        <td height="24" align="right" class="bbody" style="padding-right:5px"><b>Status</b></td>
                                        <td width="308" bgcolor="<?=$bg_color?>" style="padding-left:5px; border:solid #999 1px" class="bbody"><a title="<?= @$status['Detail'] ?>"><?= @$status['Status_Name'] ?></a>&nbsp;</td>
                                    </tr>
                                </table><br><br>
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <?php
                                        $domain_query = mysqli_query($conn, "select * from Domain where Domain_ID = '" . @$cus['Bak_Domain_ID'] . "'");
                                        //$domain = mysql_fetch_row($domain_query);
                                        $domain = mysqli_fetch_array($domain_query, MYSQLI_BOTH);
                                        ?>
                                        <td height="24" align="right" class="bbody" style="padding-right:5px">Bak</td>
                                        <td width="155" bgcolor="#999999" style="padding-left:5px; border:solid #999 1px" class="bbody"><?= @$cus['Bak_Login'] ?>&nbsp;</td>
                                        <td width="147" bgcolor="#999999" style="padding-left:5px; border:solid #999 1px" class="bbody"><?= @$domain['Doamin_Name'] ?>&nbsp;</td>
                                    </tr>
                                </table>
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <td height="24" align="right" class="bbody" style="padding-right:5px">PW</td>
                                        <td width="310" bgcolor="#999999" style="padding-left:5px; border:solid #999 1px" class="bbody"><?= @$cus['Bak_Password'] ?>&nbsp;</td>
                                    </tr>
                                </table>
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <td height="24" align="right" class="bbody" style="padding-right:5px">BIP</td>
                                        <td width="155" bgcolor="#999999" style="padding-left:5px; border:solid #999 1px; color:#00F" class="bbbody"><?= @$cus['Bak_IP'] ?>&nbsp;</td>
                                        <td width="147" bgcolor="#999999" style="padding-left:5px; border:solid #999 1px" class="bbody"><?= @$cus['Bak_IP'] ?>&nbsp;</td>
                                    </tr>
                                </table>
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <td height="24" align="right" class="bbody" style="padding-right:5px">T1</td>
                                        <td width="155" bgcolor="#ccffff" style="padding-left:5px; border:solid #999 1px" class="bbody"><?= @$cus['Tunnel1'] ?>&nbsp;</td>
                                        <td width="147" bgcolor="#ccffff" style="padding-left:5px; border:solid #999 1px" class="bbody"><?= @$cus['Tunnel1sub'] ?>&nbsp;</td>
                                    </tr>
                                </table>
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <td height="24" align="right" class="bbody" style="padding-right:5px">Install Date</td>
                                        <td width="147" bgcolor="#ffff99" class="bbody" style="padding-left:5px; border:solid #999 1px"><?= @$cus['Install_Date'] ?>&nbsp;</td>
                                    </tr>
                                </table>
                            </td>
                        </tr>

                    </table><br />
                    <hr color="#FF3333" /><br />
                </td>
            </tr>
            <tr>
                <td class="bbbody" style="padding-left:15px; padding-right:15px">EQUIPMENT<br /><br />
                    <table border="0" cellpadding="0" cellspacing="0">
                        <tr>
                            <td align="right">

                                <!--  kkk -->
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <?php
                                        $equipment_query = mysqli_query($conn, "select * from Equipment where Equipment_ID = '" . @$cus['Equipment_ID'] . "'");
                                        //$equipment = mysql_fetch_row($equipment_query);
                                        $equipment = mysqli_fetch_array($equipment_query, MYSQLI_BOTH);
                                        ?>
                                        <td height="24" align="right" class="bbody" style="padding-right:5px">Equipment</td>
                                        <td width="586" bgcolor="#ffff99" class="bbody" style="padding-left:5px; border:solid #999 1px"><?= @$equipment['Equipment_Name'] ?>&nbsp;</td>
                                    </tr>
                                </table>

                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <td height="24" align="right" class="bbody" style="padding-right:5px">Serial #</td>
                                        <td width="200" bgcolor="#ffff99" style="padding-left:5px; border:solid #999 1px" class="bbody"><?= @$cus['Serial'] ?>&nbsp;</td>
                                        <?php
                                        $antenna_query = mysqli_query($conn, "select * from Antenna where Antenna_ID = '" . @$cus['Antenna'] . "'");
                                        //  $antenna = mysql_fetch_row($antenna_query);
                                        $antenna = mysqli_fetch_array($antenna_query, MYSQLI_BOTH);
                                        ?>
                                        <td class="bbody" style="padding-right:5px; padding-left:5px">/</td>
                                        <td width="115" bgcolor="#ffff99" style="padding-left:5px; border:solid #999 1px" class="bbody"><?= @$antenna['Antenna_Name'] ?>&nbsp;</td>
                                         <td class="bbody" style="padding-right:5px; padding-left:5px">MAC</td>
                                        <td width="200" bgcolor="#ffff99" style="padding-left:5px; border:solid #999 1px" class="bbody"><?=$cus['EqMACAddr']??'' ?></td>
                                    </tr>
                                </table>

                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <?php
                                        $equipment_query = mysqli_query($conn, "select * from Equipment where Equipment_ID = '" . @$cus['UPS_ID'] . "'");
                                        // $equipment = mysql_fetch_row($equipment_query);
                                        $equipment = mysqli_fetch_array($equipment_query, MYSQLI_BOTH);
                                        ?>
                                        <td height="24" align="right" class="bbody" style="padding-right:5px">UPS</td>
                                        <td width="308" bgcolor="#ffff99" style="padding-left:5px; border:solid #999 1px" class="bbody"><?= @$equipment['Equipment_Name'] ?>&nbsp;</td>
                                        <td class="bbody" style="padding-right:5px; padding-left:10px">Serial #</td>
                                        <td width="213" bgcolor="#ffff99" style="padding-left:5px; border:solid #999 1px" class="bbody"><?= @$cus['UPS_Serial'] ?>&nbsp;</td>
                                    </tr>
                                </table>
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <td height="24" align="right" class="bbody" style="padding-right:5px">IP LAN1</td>
                                        <td width="167" bgcolor="#ffff99" style="padding-left:5px; border:solid #999 1px; color:#00F" class="bbbody"><?= @$cus['LAN_IP'] ?>&nbsp;</td>
                                        <td class="bbody" style="padding-right:5px; padding-left:5px">/</td>
                                        <td width="167" bgcolor="#ffff99" style="padding-left:5px; border:solid #999 1px; color:#00F" class="bbbody"><?= @$cus['LAN_Sub'] ?>&nbsp;</td>
                                        <td class="bbody" style="padding-right:5px">IP VIP1</td>
                                        <td width="167" bgcolor="#ffff99" style="padding-left:5px; border:solid #999 1px; color:#00F" class="bbbody"><?= @$cus['Client_LAN1_VIP1'] ?>&nbsp;</td>
                                    </tr>
                                </table>
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <td height="24" align="right" class="bbody" style="padding-right:5px">IP LAN2</td>
                                        <td width="167" bgcolor="#FFFFCC" style="padding-left:5px; border:solid #999 1px; color:#00F" class="bbbody"><?= @$cus['LAN_IP2'] ?>&nbsp;</td>
                                        <td class="bbody" style="padding-right:5px; padding-left:5px">/</td>
                                        <td width="167" bgcolor="#FFFFCC" style="padding-left:5px; border:solid #999 1px; color:#00F" class="bbbody"><?= @$cus['LAN_Sub2'] ?>&nbsp;</td>
                                        <td class="bbody" style="padding-right:5px">IP VIP2</td>
                                        <td width="167" bgcolor="#ffff99" style="padding-left:5px; border:solid #999 1px; color:#00F" class="bbbody"><?= @$cus['Client_LAN2_VIP2'] ?>&nbsp;</td>
                                    </tr>
                                </table>
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <td height="24" align="right" class="bbody" style="padding-right:5px">IP LAN3</td>
                                        <td width="167" bgcolor="#FFFFCC" style="padding-left:5px; border:solid #999 1px; color:#00F" class="bbbody"><?= @$cus['LAN_IP3'] ?>&nbsp;</td>
                                        <td class="bbody" style="padding-right:5px; padding-left:5px">/</td>
                                        <td width="167" bgcolor="#FFFFCC" style="padding-left:5px; border:solid #999 1px; color:#00F" class="bbbody"><?= @$cus['LAN_Sub3'] ?>&nbsp;</td>
                                        <td class="bbody" style="padding-right:5px">IP VIP3</td>
                                        <td width="167" bgcolor="#ffff99" style="padding-left:5px; border:solid #999 1px; color:#00F" class="bbbody"><?= @$cus['Client_LAN3_VIP3'] ?>&nbsp;</td>
                                    </tr>
                                </table>
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <td height="24" align="right" class="bbody" style="padding-right:5px">IP LAN4</td>
                                        <td width="167" bgcolor="#FFFFCC" style="padding-left:5px; border:solid #999 1px" class="bbody"><?= @$cus['Client_LAN4'] ?>&nbsp;</td>
                                        <td class="bbody" style="padding-right:5px; padding-left:5px">/</td>
                                        <td width="167" bgcolor="#FFFFCC" style="padding-left:5px; border:solid #999 1px" class="bbody"><?= @$cus['Client_LAN4_Subnet'] ?>&nbsp;</td>
                                        <td class="bbody" style="padding-right:5px;">IP VIP4</td>
                                        <td width="167" bgcolor="#ffff99" style="padding-left:5px; border:solid #999 1px" class="bbody"><?= @$cus['Client_LAN4_VIP4'] ?>&nbsp;</td>
                                    </tr>
                                </table>
                               
                            </td>
                            <td align="right" valign="top">

                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <?php
                                        $staff_query = mysqli_query($conn, "select * from Users where UID in ('" . @$cus['Profile_By'] . "', '" . @$cus['Config_By'] . "')");
                                        //$staff = mysql_fetch_row($staff_query);
                                        $staff = mysqli_fetch_array($staff_query, MYSQLI_BOTH);

                                        ?>
                                        <td height="24" align="right" class="bbody" style="padding-right:5px">Profile By</td>
                                        <td width="100" bgcolor="#ffff99" style="padding-left:5px; border:solid #999 1px" class="bbody"><?= @$staff['NickName'] ?>&nbsp;</td>
                                        <td width="" bgcolor="#ffff99" style="padding-left:5px; border:solid #999 1px" class="bbody"><?= @$cus['Config_Date'] ?>&nbsp;</td>
                                    </tr>
                                </table>
                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <?php
                                        //$staff = mysql_fetch_row($staff_query);
                                        //$staff = mysqli_fetch_array($staff_query, MYSQLI_BOTH);
                                        ?>
                                        <td height="24" align="right" class="bbody" style="padding-right:5px">Config By</td>
                                        <td width="100" bgcolor="#ffff99" style="padding-left:5px; border:solid #999 1px" class="bbody"><?= @$staff['NickName'] ?>&nbsp;</td>
                                        <td width="" bgcolor="#ffff99" style="padding-left:5px; border:solid #999 1px" class="bbody"><?= @$cus['Config_Date'] ?>&nbsp;</td>

                                    </tr>
                                </table>

                                <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <td height="24" align="right" class="bbody" style="padding-right:5px">URL CATCI MON</td>
                                        <td width="350" bgcolor="#99ccff" style="padding-left:5px; border:solid #999 1px" class="bbody">&nbsp;<?= @$cus['URL_CATCI_MON'] ?></td>

                                    </tr>
                                    <tr>
                                        <td height="24" align="right" class="bbody" style="padding-right:5px">URL PRTG MON</td>
                                        <td width="350" bgcolor="#99ccff" style="padding-left:5px; border:solid #999 1px" class="bbody">&nbsp;<?= @$cus['URL_PRTG_MON'] ?></td>

                                    </tr>
                                    <tr>
                                        <td height="24" align="right" class="bbody" style="padding-right:5px">URL WU MON</td>
                                        <td width="350" bgcolor="#99ccff" style="padding-left:5px; border:solid #999 1px" class="bbody">&nbsp;<?= @$cus['URL_WU_MON'] ?></td>

                                    </tr>
                                    <tr>
                                        <td height="24" align="right" class="bbody" style="padding-right:5px">User/Pass</td>
                                        <td width="350" bgcolor="#99ccff" style="padding-left:5px; border:solid #999 1px" class="bbody">&nbsp;<?= @$cus['Mon_User_Pass'] ?></td>

                                    </tr>
                                    <tr>
                                        <td height="24" align="right" class="bbody" style="padding-right:5px">URL Backup Config</td>
                                        <td width="350" bgcolor="#99ccff" style="padding-left:5px; border:solid #999 1px" class="bbody">&nbsp;<?= @$cus['URL_Backup_Config'] ?></td>

                                    </tr>
                                </table>


                            </td>
                        </tr>
                        <tr>
                            <td colspan="2" align="left"><br />
                                 <table border="0" cellspacing="1" cellpadding="0">
                                    <tr>
                                        <td height="24" align="right" class="bbody" style="padding-right:5px">Equipment#1</td>
                                        <td width="305" bgcolor="#ccffff" style="padding-left:5px; border:solid #999 1px" class="bbody">&nbsp;<?= @$cus['Equipment1'] ?></td>
                                        <td class="bbody" style="padding-right:5px; padding-left:10px">Serial#1</td>
                                        <td width="213" bgcolor="#ccffff" style="padding-left:5px; border:solid #999 1px" class="bbody">&nbsp;<?= @$cus['Equipment1_SN'] ?></td>
                                        <td class="bbody" style="padding-right:5px; padding-left:10px">MAC#1</td>
                                        <td width="213" bgcolor="#ccffff" style="padding-left:5px; border:solid #999 1px" class="bbody"><?=$cus['MACAddr1']??'' ?></td>

                                    </tr>
                                    <tr>
                                        <td height="24" align="right" class="bbody" style="padding-right:5px">Equipment#2</td>
                                        <td width="167" bgcolor="#ccffff" style="padding-left:5px; border:solid #999 1px" class="bbody">&nbsp;<?= @$cus['Equipment2'] ?></td>
                                        <td class="bbody" style="padding-right:5px; padding-left:10px">Serial#2</td>
                                        <td width="213" bgcolor="#ccffff" style="padding-left:5px; border:solid #999 1px" class="bbody">&nbsp;<?= @$cus['Equipment2_SN'] ?></td>
                                        <td class="bbody" style="padding-right:5px; padding-left:10px">MAC#2</td>
                                        <td width="213" bgcolor="#ccffff" style="padding-left:5px; border:solid #999 1px" class="bbody"><?=$cus['MACAddr2']??'' ?></td>
                                    </tr>
                                    <tr>
                                        <td height="24" align="right" class="bbody" style="padding-right:5px">Equipment#3</td>
                                        <td width="167" bgcolor="#ccffff" style="padding-left:5px; border:solid #999 1px" class="bbody">&nbsp;<?= @$cus['Equipment3'] ?></td>
                                        <td class="bbody" style="padding-right:5px; padding-left:10px">Serial#3</td>
                                        <td width="213" bgcolor="#ccffff" style="padding-left:5px; border:solid #999 1px" class="bbody">&nbsp;<?= @$cus['Equipment3_SN'] ?></td>
                                        <td class="bbody" style="padding-right:5px; padding-left:10px">MAC#3</td>
                                        <td width="213" bgcolor="#ccffff" style="padding-left:5px; border:solid #999 1px" class="bbody"><?=$cus['MACAddr3']??'' ?></td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="2" align="right"><br />
                                <table border="0" cellpadding="0" cellspacing="1">
                                    <tr>
                                        <td height="100" align="right" class="bbbody" style="padding-right:5px; padding-top:3px" valign="top">Remark</td>
                                        <td width="1055" valign="top" bgcolor="#FFFFFF" class="bbody" style="padding:5 5 5 5; border:solid #999 1px"><?= @nl2br(@$cus['Remark']) ?>&nbsp;</td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table><br />
                    <hr color="#FF3333" /><br />
                </td>
            </tr>
            <tr>
                <td class="bbbody" style="padding-left:15px; padding-right:15px">Brief:<br />
                    <table border="0" cellpadding="0" cellspacing="1" bgcolor="#000000" style="width: 1180px;">
                        <tr bgcolor="#CCCCCC" class="bbbody" align="center">
                            <td>CS No.(Ref)</td>
                            <td>Project ID</td>
                            <td>IROP</td>
                            <td>Site Type</td>
                            <td>Place ID</td>
                            <td>Site Name</td>
                            <td>Province</td>
                            <td>Status</td>
                            <td>Product Type</td>
                            <td>Operator</td>
                            <td>Connect No</td>
                            <td>Speed</td>
                            <td>Login</td>
                            <td>Domain</td>
                            <td>WAN IP</td>
                        </tr>
                        <tr bgcolor="#FFFFFF" class="bbody">
                            <td><?= @$cus['Ref'] ?></td>
                            <td><?= @$project['Project_Name'] ?></td>
                            <td><?= @$cus['IROP'] ?></td>
                            <td><?= @$sitetype['Site_Name'] ?></td>
                            <td><?= @$place['Shop_Name'] ?></td>
                            <td><?= @$cus['Site_Name'] ?></td>
                            <td><?= @$province['province_Name'] ?></td>
                            <td><?= @$status['Status_Name'] ?></td>
                            <td><?= @$conntype['Connection_Type'] ?></td>
                            <td><?= $m_opt ?></td>
                            <td><?= @$cus['Connect_No'] ?></td>
                            <td><?= @$speed['Speed_Name'] ?></td>
                            <td><?= @$cus['Login'] ?></td>
                            <td><?= $m_domain ?></td>
                            <td><?= @$cus['WAN_IP'] ?></td>
                        </tr>
                    </table><br />
                </td>
            </tr>
        </form>
    </table>
    <script language="JavaScript">
        <!--
        function searchKeyPress(e, b) {
            // look for window.event in case event isn't passed in
            if (window.event) {
                e = window.event;
            }
            if (e.keyCode == 13) {
                document.getElementById(b).focus();
            }
        }

        function link(l) {
            location.href = l;
        }
        //
        -->
    </script>
    <?php
    //mysqli_close($conn);
    ?>
</body></html>