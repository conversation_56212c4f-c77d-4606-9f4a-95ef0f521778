﻿<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL & ~E_NOTICE & ~E_WARNING);

date_default_timezone_set('Asia/Bangkok');

///var/www/cs/app/technic/webservice/d365bc_sync_to_csdb_tab_customers.php
///var/www/html/cs.1-to-all.com/app

define("ROOT_PATH2", '/var/www/cs/app');
require_once ROOT_PATH2 . '/defined.conf.php';
require_once ROOT_PATH2 . "/technic/db_config.php";

$mysqli = getDBConnection();
mysqli_select_db($mysqli, "KCS_DB");

/* check connection */
if (mysqli_connect_errno()) {
    printf("Connect failed: %s\n", mysqli_connect_error());
    exit();
}


if (!$mysqli->set_charset("utf8")) {
    printf("Error loading character set utf8: %s\n", $mysqli->error);
    exit();
}


require_once('global.functions.php');


/*
  @author: komsan.w
  @create: 2024

  It worked, pleae focus on permission
  on D365BC look at 'Microsoft Entra Applications' >register app
  on Azure AD look at 'App registrations'
 */
$clientId = 'dba37067-786b-4715-bcc5-90ed9d549b1a';
$clientSecret = '****************************************';
$tenantId = 'ebe5923b-b0d0-4f57-832e-37188c1f85c0';
$resource = 'https://api.businesscentral.dynamics.com';

// Get the access token
$tokenUrl = "https://login.microsoftonline.com/$tenantId/oauth2/v2.0/token";
$tokenData = array(
    'grant_type' => 'client_credentials',
    'client_id' => $clientId,
    'client_secret' => $clientSecret,
    'scope' => "$resource/.default"
);

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $tokenUrl);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($tokenData));
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
$response = curl_exec($ch);
curl_close($ch);

$json_obj = json_decode($response, true); //json_decode($response, true)['access_token'];
$token = @$json_obj['access_token'];
//echo ">>>>>>>> token:$token\n";
//print_r($json_obj);

if ($token == '') {
    echo ">>>>>>>> token: Not found!\n";
    exit;
}

$environmentname = 'ONETOALLPRODUCTION'; //'ONETOALLUAT'; //'ONETOALLPRODUCTION'; //'ONEDEV';
$companyId = '39244e7f-e71b-ee11-9cbf-6045bd1fbcd0';
$entitySetName = 'serviceOrderList'; //api name

/*
  filtering=>
  $filter=contains(Name, 'urn')
  $filter=EntryNo in (610, 612, 614)

  $filter=no eq 'CTD2400310'
  $filter=Country_Region_Code eq 'ES' and Payment_Terms_Code eq '14 DAYS'
  $filter= Country_Region_Code eq 'ES' or Country_Region_Code eq 'US'
  $filter=Entry_No lt 610
  $filter= Entry_No gt 610
  $filter=Entry_No gt 610 and Entry_No lt 615
  $filter=startswith(Name, 'S')

  $filter=SystemCreatedAt ge 2025-01-01T00:00:00Z and SystemCreatedAt le 2025-12-31T23:59:59Z


  search help key = filter-expressions
 */
// 'no' is 'No.' column
//eq is = sign
//equal to
//$filter_value= urlencode("No eq 'CTD2400310'"); // worked
//$filter = '$filter='.$filter_value; // worked
//startswith
//$filter_value= urlencode(" 'CTD24004'");//worked
//$filter = "\$filter=startswith(No,$filter_value)" ; // worked

//$filter_value = urlencode("startswith(No,'CTD') and (SystemCreatedAt ge 2024-01-01T00:00:00Z and SystemCreatedAt le 2024-12-31T23:59:59Z)");

$yesterday = date('Y-m-d', strtotime('-1 day'));
$date_lookup = $yesterday; // '2025-01-01';// $yesterday ;

//$filter_value = urlencode("(Status eq 'Finished') and (ServiceOrderType eq 'SVO-11') and (SystemModifiedAt ge ".$date_lookup."T00:00:00Z)");

$filter_value = urlencode("(Status eq 'Finished') and (ServiceOrderType eq 'SVO-11')");
$filter = '$filter=' . $filter_value;

/*
  in AL code
  APIPublisher = '1ToAll';
  APIGroup = 'ONE_API';
  APIVersion = 'v1.0';
  companies($companyId) or companies('company name')
  EntitySetName = 'customerLists'; //app name
 */
$endpoint = "$tenantId/$environmentname/api/1ToAll/ONE_API/v1.0/companies($companyId)/$entitySetName?$filter";
// Make an API request
$apiUrl = "$resource/v2.0/$endpoint";

//$apiUrl = 'https://api.businesscentral.dynamics.com/v2.0/ebe5923b-b0d0-4f57-832e-37188c1f85c0/ONEDEV/api/1ToAll/ONE_API/v1.0/companies(39244e7f-e71b-ee11-9cbf-6045bd1fbcd0)/customerLists?$filter=no%20eq%20%27CTD2400310%27';
//$apiUrl = 'https://api.businesscentral.dynamics.com/v2.0/ebe5923b-b0d0-4f57-832e-37188c1f85c0/ONEDEV/api/1ToAll/ONE_API/v1.0/companies(39244e7f-e71b-ee11-9cbf-6045bd1fbcd0)/customerLists?$filter=startswith(no,%20%27CTD24004%27)';

echo ">>>>>>>> apiUrl:$apiUrl\n";

$headers = array(
    "Authorization: Bearer $token",
    "Content-Type: application/json"
);

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $apiUrl);
curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
$response = curl_exec($ch);
curl_close($ch);

$results = json_decode($response, true);
//true = Decode JSON data into an associative array $array['name'];
//false = Decode JSON data into a PHP object  $object->name;
//echo "\n>>Found value records: ".count($results['value'])."\n";
//print_r($results);

$rows_count = 0;
if (is_array($results) && count($results['value']) > 0) {
    echo "\n-----------------Starting----------------\n";
    foreach ($results['value'] as $row) {

        /*
            [No] => SVO2503-0007
            [Status] => Finished
            [ServiceOrderType] => SVO-11
            [ServiceOrderType_Desc] => Cancellation/Terminate
            [Customer_No] => CTD2301462
            [Bill_to_Name] => บริษัท บอนกุ เทคโนโลจีส์ จำกัด
            [Bill_to_Address] => 944 อาคารสามย่านมิตรทาวน์ ชั้น 24-25 ถนนพระราม 4 แขวงวังใหม่
            [Bill_to_Address2] => เขตปทุมวัน
            [Bill_to_City] => กรุงเทพมหานคร
            [Bill_to_Contact] =>
            [Ship_to_Code] =>
            [Ship_to_Name] => บริษัท บอนกุ เทคโนโลจีส์ จำกัด
            [Ship_to_Address] => 944 อาคารสามย่านมิตรทาวน์ ชั้น 24-25 ถนนพระราม 4 แขวงวังใหม่
            [Ship_to_Address2] => เขตปทุมวัน
            [Ship_to_City] => กรุงเทพมหานคร
            [Ship_to_Contact] =>
            [Ship_to_Phone] => 085-9565591,02-1718666
          echo "SystemCreatedAt: ".toDateString($row['SystemCreatedAt'])."\n";
          echo "SystemModifiedAt: ".toDateString($row['SystemModifiedAt'])."\n";
          echo "-----------------------------------\n";
         */
        //loop fix null value $row
        foreach ($row as $key => $value) {
            if ($value == null) {
                $row[$key] = '';
            }else
            {
                $row[$key] = htmlspecialchars($value);
            }
        }
       
        if (SyncToCSDB($row['No'], $row['Status'], $row['ServiceOrderType'], $row['ServiceOrderType_Desc'], $row['Customer_No'], $row['Bill_to_Name'], $row['Bill_to_Address'], $row['Bill_to_Address2'], $row['Bill_to_City'], $row['Bill_to_Contact'], $row['Ship_to_Code'], $row['Ship_to_Name'], $row['Ship_to_Address'], $row['Ship_to_Address2'],
            $row['Ship_to_City'], $row['Ship_to_Contact'], $row['Ship_to_Phone'])) {
            $rows_count++;
        }
        echo ">" . $row['No'] . " " . $row['ServiceOrderType_Desc'] . "   \n";
    }
    echo "\n-------End----found->$rows_count new rows synced----------\n";
}

function toDateString($datetime)
{
    try {
        return date('Y-m-d', strtotime($datetime));
    } catch (Exception $e) {
        return $datetime;
    }
}

function SyncToCSDB(
    $No,
    $Status,
    $ServiceOrderType,
    $ServiceOrderType_Desc,
    $Customer_No,
    $Bill_to_Name,
    $Bill_to_Address,
    $Bill_to_Address2,
    $Bill_to_City,
    $Bill_to_Contact,
    $Ship_to_Code,
    $Ship_to_Name,
    $Ship_to_Address,
    $Ship_to_Address2,
    $Ship_to_City,
    $Ship_to_Contact,
    $Ship_to_Phone
) {
    global $mysqli;
    $retval = FALSE;
    $responseCode = 200;

    $sql_insert = "INSERT INTO D365BC_ServiceOrder( `No`,`Status`,`ServiceOrderType`,`ServiceOrderType_Desc`,
	`Customer_No`,`Bill_to_Name`,`Bill_to_Address`,	`Bill_to_Address2`,	`Bill_to_City`,	`Bill_to_Contact`,
	`Ship_to_Code`,	`Ship_to_Name`,	`Ship_to_Address`,	`Ship_to_Address2`,	`Ship_to_City`,	`Ship_to_Contact`,
	`Ship_to_Phone` )
	VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?);";

    $sql_select = "SELECT SQL_CALC_FOUND_ROWS * FROM D365BC_ServiceOrder WHERE `No` ='$No';";

    if (!$No) {
        $responseCode = 402;
        //$retval['message'] =''please enter customer no.;
    }
    if (strlen($No) < 3 && $responseCode == 200) {
        $responseCode = 403;
        //$retval['message'] =''please enter customer name;
    }

    if ($responseCode == 200) {
        //check duplicate
        if ($stmt = $mysqli->query($sql_select)) {
            $found_row = FoundRows();
            if ($found_row == null) {
                $found_row = 0;
            }
            if ($found_row != 0) {
                $responseCode = 400;
                //$retval['status'] = 'fail';
                //$retval['message'] = "Error Customer No. : $CusCode Already exist! ";
            } else {
                $stmt = $mysqli->prepare($sql_insert);
                $stmt->bind_param(
                    "sssssssssssssssss",
                    $No,
                    $Status,
                    $ServiceOrderType,
                    $ServiceOrderType_Desc,
                    $Customer_No,
                    $Bill_to_Name,
                    $Bill_to_Address,
                    $Bill_to_Address2,
                    $Bill_to_City,
                    $Bill_to_Contact,
                    $Ship_to_Code,
                    $Ship_to_Name,
                    $Ship_to_Address,
                    $Ship_to_Address2,
                    $Ship_to_City,
                    $Ship_to_Contact,
                    $Ship_to_Phone
                );

                if ($stmt->execute()) {
                    //$retval = array();
                    //$retval['status'] = 'success';
                    //$retval['message'] = 'data have been inserted';
                    $retval = TRUE;
                } else {
                    $responseCode = 400;
                    //$retval['status'] = 'fail';
                    //$retval['message'] = "mysqli prepare failed: ($sql_insert)";
                }
            }
        } else {
            $responseCode = 400;
            //$retval['status'] = 'fail';
            //$retval['message'] = "mysqli prepare failed: ($sql_select)";
        }
    }
    return $retval;
}
